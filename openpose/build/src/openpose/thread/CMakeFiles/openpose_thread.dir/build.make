# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/eigenPose/openpose

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/eigenPose/openpose/build

# Include any dependencies generated for this target.
include src/openpose/thread/CMakeFiles/openpose_thread.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/openpose/thread/CMakeFiles/openpose_thread.dir/compiler_depend.make

# Include the progress variables for this target.
include src/openpose/thread/CMakeFiles/openpose_thread.dir/progress.make

# Include the compile flags for this target's objects.
include src/openpose/thread/CMakeFiles/openpose_thread.dir/flags.make

src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o: src/openpose/thread/CMakeFiles/openpose_thread.dir/flags.make
src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o: ../src/openpose/thread/defineTemplates.cpp
src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o: src/openpose/thread/CMakeFiles/openpose_thread.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/thread && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o -MF CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o.d -o CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o -c /home/<USER>/eigenPose/openpose/src/openpose/thread/defineTemplates.cpp

src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/openpose_thread.dir/defineTemplates.cpp.i"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/thread && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/eigenPose/openpose/src/openpose/thread/defineTemplates.cpp > CMakeFiles/openpose_thread.dir/defineTemplates.cpp.i

src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/openpose_thread.dir/defineTemplates.cpp.s"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/thread && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/eigenPose/openpose/src/openpose/thread/defineTemplates.cpp -o CMakeFiles/openpose_thread.dir/defineTemplates.cpp.s

# Object files for target openpose_thread
openpose_thread_OBJECTS = \
"CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o"

# External object files for target openpose_thread
openpose_thread_EXTERNAL_OBJECTS =

src/openpose/thread/libopenpose_thread.so: src/openpose/thread/CMakeFiles/openpose_thread.dir/defineTemplates.cpp.o
src/openpose/thread/libopenpose_thread.so: src/openpose/thread/CMakeFiles/openpose_thread.dir/build.make
src/openpose/thread/libopenpose_thread.so: src/openpose/core/libopenpose_core.so
src/openpose/thread/libopenpose_thread.so: src/openpose/thread/CMakeFiles/openpose_thread.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library libopenpose_thread.so"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/thread && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/openpose_thread.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/openpose/thread/CMakeFiles/openpose_thread.dir/build: src/openpose/thread/libopenpose_thread.so
.PHONY : src/openpose/thread/CMakeFiles/openpose_thread.dir/build

src/openpose/thread/CMakeFiles/openpose_thread.dir/clean:
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/thread && $(CMAKE_COMMAND) -P CMakeFiles/openpose_thread.dir/cmake_clean.cmake
.PHONY : src/openpose/thread/CMakeFiles/openpose_thread.dir/clean

src/openpose/thread/CMakeFiles/openpose_thread.dir/depend:
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/eigenPose/openpose /home/<USER>/eigenPose/openpose/src/openpose/thread /home/<USER>/eigenPose/openpose/build /home/<USER>/eigenPose/openpose/build/src/openpose/thread /home/<USER>/eigenPose/openpose/build/src/openpose/thread/CMakeFiles/openpose_thread.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/openpose/thread/CMakeFiles/openpose_thread.dir/depend

