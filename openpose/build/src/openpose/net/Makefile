# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/eigenPose/openpose

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/eigenPose/openpose/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/eigenPose/openpose/build/CMakeFiles /home/<USER>/eigenPose/openpose/build/src/openpose/net//CMakeFiles/progress.marks
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/net/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/eigenPose/openpose/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/net/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/net/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/net/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/openpose/net/CMakeFiles/openpose_net.dir/rule:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/net/CMakeFiles/openpose_net.dir/rule
.PHONY : src/openpose/net/CMakeFiles/openpose_net.dir/rule

# Convenience name for target.
openpose_net: src/openpose/net/CMakeFiles/openpose_net.dir/rule
.PHONY : openpose_net

# fast build rule for target.
openpose_net/fast:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/build
.PHONY : openpose_net/fast

bodyPartConnectorBase.o: bodyPartConnectorBase.cpp.o
.PHONY : bodyPartConnectorBase.o

# target to build an object file
bodyPartConnectorBase.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorBase.cpp.o
.PHONY : bodyPartConnectorBase.cpp.o

bodyPartConnectorBase.i: bodyPartConnectorBase.cpp.i
.PHONY : bodyPartConnectorBase.i

# target to preprocess a source file
bodyPartConnectorBase.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorBase.cpp.i
.PHONY : bodyPartConnectorBase.cpp.i

bodyPartConnectorBase.s: bodyPartConnectorBase.cpp.s
.PHONY : bodyPartConnectorBase.s

# target to generate assembly for a file
bodyPartConnectorBase.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorBase.cpp.s
.PHONY : bodyPartConnectorBase.cpp.s

bodyPartConnectorBaseCL.o: bodyPartConnectorBaseCL.cpp.o
.PHONY : bodyPartConnectorBaseCL.o

# target to build an object file
bodyPartConnectorBaseCL.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorBaseCL.cpp.o
.PHONY : bodyPartConnectorBaseCL.cpp.o

bodyPartConnectorBaseCL.i: bodyPartConnectorBaseCL.cpp.i
.PHONY : bodyPartConnectorBaseCL.i

# target to preprocess a source file
bodyPartConnectorBaseCL.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorBaseCL.cpp.i
.PHONY : bodyPartConnectorBaseCL.cpp.i

bodyPartConnectorBaseCL.s: bodyPartConnectorBaseCL.cpp.s
.PHONY : bodyPartConnectorBaseCL.s

# target to generate assembly for a file
bodyPartConnectorBaseCL.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorBaseCL.cpp.s
.PHONY : bodyPartConnectorBaseCL.cpp.s

bodyPartConnectorCaffe.o: bodyPartConnectorCaffe.cpp.o
.PHONY : bodyPartConnectorCaffe.o

# target to build an object file
bodyPartConnectorCaffe.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorCaffe.cpp.o
.PHONY : bodyPartConnectorCaffe.cpp.o

bodyPartConnectorCaffe.i: bodyPartConnectorCaffe.cpp.i
.PHONY : bodyPartConnectorCaffe.i

# target to preprocess a source file
bodyPartConnectorCaffe.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorCaffe.cpp.i
.PHONY : bodyPartConnectorCaffe.cpp.i

bodyPartConnectorCaffe.s: bodyPartConnectorCaffe.cpp.s
.PHONY : bodyPartConnectorCaffe.s

# target to generate assembly for a file
bodyPartConnectorCaffe.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/bodyPartConnectorCaffe.cpp.s
.PHONY : bodyPartConnectorCaffe.cpp.s

maximumBase.o: maximumBase.cpp.o
.PHONY : maximumBase.o

# target to build an object file
maximumBase.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/maximumBase.cpp.o
.PHONY : maximumBase.cpp.o

maximumBase.i: maximumBase.cpp.i
.PHONY : maximumBase.i

# target to preprocess a source file
maximumBase.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/maximumBase.cpp.i
.PHONY : maximumBase.cpp.i

maximumBase.s: maximumBase.cpp.s
.PHONY : maximumBase.s

# target to generate assembly for a file
maximumBase.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/maximumBase.cpp.s
.PHONY : maximumBase.cpp.s

maximumCaffe.o: maximumCaffe.cpp.o
.PHONY : maximumCaffe.o

# target to build an object file
maximumCaffe.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/maximumCaffe.cpp.o
.PHONY : maximumCaffe.cpp.o

maximumCaffe.i: maximumCaffe.cpp.i
.PHONY : maximumCaffe.i

# target to preprocess a source file
maximumCaffe.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/maximumCaffe.cpp.i
.PHONY : maximumCaffe.cpp.i

maximumCaffe.s: maximumCaffe.cpp.s
.PHONY : maximumCaffe.s

# target to generate assembly for a file
maximumCaffe.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/maximumCaffe.cpp.s
.PHONY : maximumCaffe.cpp.s

netCaffe.o: netCaffe.cpp.o
.PHONY : netCaffe.o

# target to build an object file
netCaffe.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/netCaffe.cpp.o
.PHONY : netCaffe.cpp.o

netCaffe.i: netCaffe.cpp.i
.PHONY : netCaffe.i

# target to preprocess a source file
netCaffe.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/netCaffe.cpp.i
.PHONY : netCaffe.cpp.i

netCaffe.s: netCaffe.cpp.s
.PHONY : netCaffe.s

# target to generate assembly for a file
netCaffe.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/netCaffe.cpp.s
.PHONY : netCaffe.cpp.s

netOpenCv.o: netOpenCv.cpp.o
.PHONY : netOpenCv.o

# target to build an object file
netOpenCv.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/netOpenCv.cpp.o
.PHONY : netOpenCv.cpp.o

netOpenCv.i: netOpenCv.cpp.i
.PHONY : netOpenCv.i

# target to preprocess a source file
netOpenCv.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/netOpenCv.cpp.i
.PHONY : netOpenCv.cpp.i

netOpenCv.s: netOpenCv.cpp.s
.PHONY : netOpenCv.s

# target to generate assembly for a file
netOpenCv.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/netOpenCv.cpp.s
.PHONY : netOpenCv.cpp.s

nmsBase.o: nmsBase.cpp.o
.PHONY : nmsBase.o

# target to build an object file
nmsBase.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsBase.cpp.o
.PHONY : nmsBase.cpp.o

nmsBase.i: nmsBase.cpp.i
.PHONY : nmsBase.i

# target to preprocess a source file
nmsBase.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsBase.cpp.i
.PHONY : nmsBase.cpp.i

nmsBase.s: nmsBase.cpp.s
.PHONY : nmsBase.s

# target to generate assembly for a file
nmsBase.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsBase.cpp.s
.PHONY : nmsBase.cpp.s

nmsBaseCL.o: nmsBaseCL.cpp.o
.PHONY : nmsBaseCL.o

# target to build an object file
nmsBaseCL.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsBaseCL.cpp.o
.PHONY : nmsBaseCL.cpp.o

nmsBaseCL.i: nmsBaseCL.cpp.i
.PHONY : nmsBaseCL.i

# target to preprocess a source file
nmsBaseCL.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsBaseCL.cpp.i
.PHONY : nmsBaseCL.cpp.i

nmsBaseCL.s: nmsBaseCL.cpp.s
.PHONY : nmsBaseCL.s

# target to generate assembly for a file
nmsBaseCL.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsBaseCL.cpp.s
.PHONY : nmsBaseCL.cpp.s

nmsCaffe.o: nmsCaffe.cpp.o
.PHONY : nmsCaffe.o

# target to build an object file
nmsCaffe.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsCaffe.cpp.o
.PHONY : nmsCaffe.cpp.o

nmsCaffe.i: nmsCaffe.cpp.i
.PHONY : nmsCaffe.i

# target to preprocess a source file
nmsCaffe.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsCaffe.cpp.i
.PHONY : nmsCaffe.cpp.i

nmsCaffe.s: nmsCaffe.cpp.s
.PHONY : nmsCaffe.s

# target to generate assembly for a file
nmsCaffe.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/nmsCaffe.cpp.s
.PHONY : nmsCaffe.cpp.s

resizeAndMergeBase.o: resizeAndMergeBase.cpp.o
.PHONY : resizeAndMergeBase.o

# target to build an object file
resizeAndMergeBase.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeBase.cpp.o
.PHONY : resizeAndMergeBase.cpp.o

resizeAndMergeBase.i: resizeAndMergeBase.cpp.i
.PHONY : resizeAndMergeBase.i

# target to preprocess a source file
resizeAndMergeBase.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeBase.cpp.i
.PHONY : resizeAndMergeBase.cpp.i

resizeAndMergeBase.s: resizeAndMergeBase.cpp.s
.PHONY : resizeAndMergeBase.s

# target to generate assembly for a file
resizeAndMergeBase.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeBase.cpp.s
.PHONY : resizeAndMergeBase.cpp.s

resizeAndMergeBaseCL.o: resizeAndMergeBaseCL.cpp.o
.PHONY : resizeAndMergeBaseCL.o

# target to build an object file
resizeAndMergeBaseCL.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeBaseCL.cpp.o
.PHONY : resizeAndMergeBaseCL.cpp.o

resizeAndMergeBaseCL.i: resizeAndMergeBaseCL.cpp.i
.PHONY : resizeAndMergeBaseCL.i

# target to preprocess a source file
resizeAndMergeBaseCL.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeBaseCL.cpp.i
.PHONY : resizeAndMergeBaseCL.cpp.i

resizeAndMergeBaseCL.s: resizeAndMergeBaseCL.cpp.s
.PHONY : resizeAndMergeBaseCL.s

# target to generate assembly for a file
resizeAndMergeBaseCL.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeBaseCL.cpp.s
.PHONY : resizeAndMergeBaseCL.cpp.s

resizeAndMergeCaffe.o: resizeAndMergeCaffe.cpp.o
.PHONY : resizeAndMergeCaffe.o

# target to build an object file
resizeAndMergeCaffe.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeCaffe.cpp.o
.PHONY : resizeAndMergeCaffe.cpp.o

resizeAndMergeCaffe.i: resizeAndMergeCaffe.cpp.i
.PHONY : resizeAndMergeCaffe.i

# target to preprocess a source file
resizeAndMergeCaffe.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeCaffe.cpp.i
.PHONY : resizeAndMergeCaffe.cpp.i

resizeAndMergeCaffe.s: resizeAndMergeCaffe.cpp.s
.PHONY : resizeAndMergeCaffe.s

# target to generate assembly for a file
resizeAndMergeCaffe.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/net/CMakeFiles/openpose_net.dir/build.make src/openpose/net/CMakeFiles/openpose_net.dir/resizeAndMergeCaffe.cpp.s
.PHONY : resizeAndMergeCaffe.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... openpose_net"
	@echo "... bodyPartConnectorBase.o"
	@echo "... bodyPartConnectorBase.i"
	@echo "... bodyPartConnectorBase.s"
	@echo "... bodyPartConnectorBaseCL.o"
	@echo "... bodyPartConnectorBaseCL.i"
	@echo "... bodyPartConnectorBaseCL.s"
	@echo "... bodyPartConnectorCaffe.o"
	@echo "... bodyPartConnectorCaffe.i"
	@echo "... bodyPartConnectorCaffe.s"
	@echo "... maximumBase.o"
	@echo "... maximumBase.i"
	@echo "... maximumBase.s"
	@echo "... maximumCaffe.o"
	@echo "... maximumCaffe.i"
	@echo "... maximumCaffe.s"
	@echo "... netCaffe.o"
	@echo "... netCaffe.i"
	@echo "... netCaffe.s"
	@echo "... netOpenCv.o"
	@echo "... netOpenCv.i"
	@echo "... netOpenCv.s"
	@echo "... nmsBase.o"
	@echo "... nmsBase.i"
	@echo "... nmsBase.s"
	@echo "... nmsBaseCL.o"
	@echo "... nmsBaseCL.i"
	@echo "... nmsBaseCL.s"
	@echo "... nmsCaffe.o"
	@echo "... nmsCaffe.i"
	@echo "... nmsCaffe.s"
	@echo "... resizeAndMergeBase.o"
	@echo "... resizeAndMergeBase.i"
	@echo "... resizeAndMergeBase.s"
	@echo "... resizeAndMergeBaseCL.o"
	@echo "... resizeAndMergeBaseCL.i"
	@echo "... resizeAndMergeBaseCL.s"
	@echo "... resizeAndMergeCaffe.o"
	@echo "... resizeAndMergeCaffe.i"
	@echo "... resizeAndMergeCaffe.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

