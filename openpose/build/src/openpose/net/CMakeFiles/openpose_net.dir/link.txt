/usr/bin/c++ -fPIC  -fopenmp  -fopenmp -O3 -shared -Wl,-soname,libopenpose_net.so -o libopenpose_net.so CMakeFiles/openpose_net.dir/bodyPartConnectorBase.cpp.o CMakeFiles/openpose_net.dir/bodyPartConnectorBaseCL.cpp.o CMakeFiles/openpose_net.dir/bodyPartConnectorCaffe.cpp.o CMakeFiles/openpose_net.dir/maximumBase.cpp.o CMakeFiles/openpose_net.dir/maximumCaffe.cpp.o CMakeFiles/openpose_net.dir/netCaffe.cpp.o CMakeFiles/openpose_net.dir/netOpenCv.cpp.o CMakeFiles/openpose_net.dir/nmsBase.cpp.o CMakeFiles/openpose_net.dir/nmsBaseCL.cpp.o CMakeFiles/openpose_net.dir/nmsCaffe.cpp.o CMakeFiles/openpose_net.dir/resizeAndMergeBase.cpp.o CMakeFiles/openpose_net.dir/resizeAndMergeBaseCL.cpp.o CMakeFiles/openpose_net.dir/resizeAndMergeCaffe.cpp.o  -Wl,-rpath,/home/<USER>/eigenPose/openpose/build/caffe/lib:/home/<USER>/eigenPose/openpose/build/src/openpose/core: ../../../caffe/lib/libcaffe.so ../core/libopenpose_core.so 
