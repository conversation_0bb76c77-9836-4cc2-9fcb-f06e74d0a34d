# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/eigenPose/openpose

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/eigenPose/openpose/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/eigenPose/openpose/build/CMakeFiles /home/<USER>/eigenPose/openpose/build/src/openpose/pose//CMakeFiles/progress.marks
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/pose/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/eigenPose/openpose/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/pose/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/pose/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/pose/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/openpose/pose/CMakeFiles/openpose_pose.dir/rule:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/openpose/pose/CMakeFiles/openpose_pose.dir/rule
.PHONY : src/openpose/pose/CMakeFiles/openpose_pose.dir/rule

# Convenience name for target.
openpose_pose: src/openpose/pose/CMakeFiles/openpose_pose.dir/rule
.PHONY : openpose_pose

# fast build rule for target.
openpose_pose/fast:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/build
.PHONY : openpose_pose/fast

defineTemplates.o: defineTemplates.cpp.o
.PHONY : defineTemplates.o

# target to build an object file
defineTemplates.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/defineTemplates.cpp.o
.PHONY : defineTemplates.cpp.o

defineTemplates.i: defineTemplates.cpp.i
.PHONY : defineTemplates.i

# target to preprocess a source file
defineTemplates.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/defineTemplates.cpp.i
.PHONY : defineTemplates.cpp.i

defineTemplates.s: defineTemplates.cpp.s
.PHONY : defineTemplates.s

# target to generate assembly for a file
defineTemplates.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/defineTemplates.cpp.s
.PHONY : defineTemplates.cpp.s

poseCpuRenderer.o: poseCpuRenderer.cpp.o
.PHONY : poseCpuRenderer.o

# target to build an object file
poseCpuRenderer.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseCpuRenderer.cpp.o
.PHONY : poseCpuRenderer.cpp.o

poseCpuRenderer.i: poseCpuRenderer.cpp.i
.PHONY : poseCpuRenderer.i

# target to preprocess a source file
poseCpuRenderer.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseCpuRenderer.cpp.i
.PHONY : poseCpuRenderer.cpp.i

poseCpuRenderer.s: poseCpuRenderer.cpp.s
.PHONY : poseCpuRenderer.s

# target to generate assembly for a file
poseCpuRenderer.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseCpuRenderer.cpp.s
.PHONY : poseCpuRenderer.cpp.s

poseExtractor.o: poseExtractor.cpp.o
.PHONY : poseExtractor.o

# target to build an object file
poseExtractor.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractor.cpp.o
.PHONY : poseExtractor.cpp.o

poseExtractor.i: poseExtractor.cpp.i
.PHONY : poseExtractor.i

# target to preprocess a source file
poseExtractor.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractor.cpp.i
.PHONY : poseExtractor.cpp.i

poseExtractor.s: poseExtractor.cpp.s
.PHONY : poseExtractor.s

# target to generate assembly for a file
poseExtractor.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractor.cpp.s
.PHONY : poseExtractor.cpp.s

poseExtractorCaffe.o: poseExtractorCaffe.cpp.o
.PHONY : poseExtractorCaffe.o

# target to build an object file
poseExtractorCaffe.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractorCaffe.cpp.o
.PHONY : poseExtractorCaffe.cpp.o

poseExtractorCaffe.i: poseExtractorCaffe.cpp.i
.PHONY : poseExtractorCaffe.i

# target to preprocess a source file
poseExtractorCaffe.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractorCaffe.cpp.i
.PHONY : poseExtractorCaffe.cpp.i

poseExtractorCaffe.s: poseExtractorCaffe.cpp.s
.PHONY : poseExtractorCaffe.s

# target to generate assembly for a file
poseExtractorCaffe.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractorCaffe.cpp.s
.PHONY : poseExtractorCaffe.cpp.s

poseExtractorNet.o: poseExtractorNet.cpp.o
.PHONY : poseExtractorNet.o

# target to build an object file
poseExtractorNet.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractorNet.cpp.o
.PHONY : poseExtractorNet.cpp.o

poseExtractorNet.i: poseExtractorNet.cpp.i
.PHONY : poseExtractorNet.i

# target to preprocess a source file
poseExtractorNet.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractorNet.cpp.i
.PHONY : poseExtractorNet.cpp.i

poseExtractorNet.s: poseExtractorNet.cpp.s
.PHONY : poseExtractorNet.s

# target to generate assembly for a file
poseExtractorNet.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseExtractorNet.cpp.s
.PHONY : poseExtractorNet.cpp.s

poseGpuRenderer.o: poseGpuRenderer.cpp.o
.PHONY : poseGpuRenderer.o

# target to build an object file
poseGpuRenderer.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseGpuRenderer.cpp.o
.PHONY : poseGpuRenderer.cpp.o

poseGpuRenderer.i: poseGpuRenderer.cpp.i
.PHONY : poseGpuRenderer.i

# target to preprocess a source file
poseGpuRenderer.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseGpuRenderer.cpp.i
.PHONY : poseGpuRenderer.cpp.i

poseGpuRenderer.s: poseGpuRenderer.cpp.s
.PHONY : poseGpuRenderer.s

# target to generate assembly for a file
poseGpuRenderer.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseGpuRenderer.cpp.s
.PHONY : poseGpuRenderer.cpp.s

poseParameters.o: poseParameters.cpp.o
.PHONY : poseParameters.o

# target to build an object file
poseParameters.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseParameters.cpp.o
.PHONY : poseParameters.cpp.o

poseParameters.i: poseParameters.cpp.i
.PHONY : poseParameters.i

# target to preprocess a source file
poseParameters.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseParameters.cpp.i
.PHONY : poseParameters.cpp.i

poseParameters.s: poseParameters.cpp.s
.PHONY : poseParameters.s

# target to generate assembly for a file
poseParameters.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseParameters.cpp.s
.PHONY : poseParameters.cpp.s

poseParametersRender.o: poseParametersRender.cpp.o
.PHONY : poseParametersRender.o

# target to build an object file
poseParametersRender.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseParametersRender.cpp.o
.PHONY : poseParametersRender.cpp.o

poseParametersRender.i: poseParametersRender.cpp.i
.PHONY : poseParametersRender.i

# target to preprocess a source file
poseParametersRender.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseParametersRender.cpp.i
.PHONY : poseParametersRender.cpp.i

poseParametersRender.s: poseParametersRender.cpp.s
.PHONY : poseParametersRender.s

# target to generate assembly for a file
poseParametersRender.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseParametersRender.cpp.s
.PHONY : poseParametersRender.cpp.s

poseRenderer.o: poseRenderer.cpp.o
.PHONY : poseRenderer.o

# target to build an object file
poseRenderer.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseRenderer.cpp.o
.PHONY : poseRenderer.cpp.o

poseRenderer.i: poseRenderer.cpp.i
.PHONY : poseRenderer.i

# target to preprocess a source file
poseRenderer.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseRenderer.cpp.i
.PHONY : poseRenderer.cpp.i

poseRenderer.s: poseRenderer.cpp.s
.PHONY : poseRenderer.s

# target to generate assembly for a file
poseRenderer.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/poseRenderer.cpp.s
.PHONY : poseRenderer.cpp.s

renderPose.o: renderPose.cpp.o
.PHONY : renderPose.o

# target to build an object file
renderPose.cpp.o:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/renderPose.cpp.o
.PHONY : renderPose.cpp.o

renderPose.i: renderPose.cpp.i
.PHONY : renderPose.i

# target to preprocess a source file
renderPose.cpp.i:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/renderPose.cpp.i
.PHONY : renderPose.cpp.i

renderPose.s: renderPose.cpp.s
.PHONY : renderPose.s

# target to generate assembly for a file
renderPose.cpp.s:
	cd /home/<USER>/eigenPose/openpose/build && $(MAKE) $(MAKESILENT) -f src/openpose/pose/CMakeFiles/openpose_pose.dir/build.make src/openpose/pose/CMakeFiles/openpose_pose.dir/renderPose.cpp.s
.PHONY : renderPose.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... openpose_pose"
	@echo "... defineTemplates.o"
	@echo "... defineTemplates.i"
	@echo "... defineTemplates.s"
	@echo "... poseCpuRenderer.o"
	@echo "... poseCpuRenderer.i"
	@echo "... poseCpuRenderer.s"
	@echo "... poseExtractor.o"
	@echo "... poseExtractor.i"
	@echo "... poseExtractor.s"
	@echo "... poseExtractorCaffe.o"
	@echo "... poseExtractorCaffe.i"
	@echo "... poseExtractorCaffe.s"
	@echo "... poseExtractorNet.o"
	@echo "... poseExtractorNet.i"
	@echo "... poseExtractorNet.s"
	@echo "... poseGpuRenderer.o"
	@echo "... poseGpuRenderer.i"
	@echo "... poseGpuRenderer.s"
	@echo "... poseParameters.o"
	@echo "... poseParameters.i"
	@echo "... poseParameters.s"
	@echo "... poseParametersRender.o"
	@echo "... poseParametersRender.i"
	@echo "... poseParametersRender.s"
	@echo "... poseRenderer.o"
	@echo "... poseRenderer.i"
	@echo "... poseRenderer.s"
	@echo "... renderPose.o"
	@echo "... renderPose.i"
	@echo "... renderPose.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

