#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "openpose_3d" for configuration "Release"
set_property(TARGET openpose_3d APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_3d PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_3d.so"
  IMPORTED_SONAME_RELEASE "libopenpose_3d.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_3d )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_3d "${_IMPORT_PREFIX}/lib/libopenpose_3d.so" )

# Import target "openpose_calibration" for configuration "Release"
set_property(TARGET openpose_calibration APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_calibration PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_calibration.so"
  IMPORTED_SONAME_RELEASE "libopenpose_calibration.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_calibration )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_calibration "${_IMPORT_PREFIX}/lib/libopenpose_calibration.so" )

# Import target "openpose_core" for configuration "Release"
set_property(TARGET openpose_core APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_core PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_core.so"
  IMPORTED_SONAME_RELEASE "libopenpose_core.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_core )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_core "${_IMPORT_PREFIX}/lib/libopenpose_core.so" )

# Import target "openpose_face" for configuration "Release"
set_property(TARGET openpose_face APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_face PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_face.so"
  IMPORTED_SONAME_RELEASE "libopenpose_face.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_face )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_face "${_IMPORT_PREFIX}/lib/libopenpose_face.so" )

# Import target "openpose_filestream" for configuration "Release"
set_property(TARGET openpose_filestream APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_filestream PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_filestream.so"
  IMPORTED_SONAME_RELEASE "libopenpose_filestream.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_filestream )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_filestream "${_IMPORT_PREFIX}/lib/libopenpose_filestream.so" )

# Import target "openpose_gpu" for configuration "Release"
set_property(TARGET openpose_gpu APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_gpu PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_gpu.so"
  IMPORTED_SONAME_RELEASE "libopenpose_gpu.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_gpu )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_gpu "${_IMPORT_PREFIX}/lib/libopenpose_gpu.so" )

# Import target "openpose_gui" for configuration "Release"
set_property(TARGET openpose_gui APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_gui PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_gui.so"
  IMPORTED_SONAME_RELEASE "libopenpose_gui.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_gui )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_gui "${_IMPORT_PREFIX}/lib/libopenpose_gui.so" )

# Import target "openpose_hand" for configuration "Release"
set_property(TARGET openpose_hand APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_hand PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_hand.so"
  IMPORTED_SONAME_RELEASE "libopenpose_hand.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_hand )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_hand "${_IMPORT_PREFIX}/lib/libopenpose_hand.so" )

# Import target "openpose_net" for configuration "Release"
set_property(TARGET openpose_net APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_net PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_net.so"
  IMPORTED_SONAME_RELEASE "libopenpose_net.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_net )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_net "${_IMPORT_PREFIX}/lib/libopenpose_net.so" )

# Import target "openpose_pose" for configuration "Release"
set_property(TARGET openpose_pose APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_pose PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_pose.so"
  IMPORTED_SONAME_RELEASE "libopenpose_pose.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_pose )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_pose "${_IMPORT_PREFIX}/lib/libopenpose_pose.so" )

# Import target "openpose_producer" for configuration "Release"
set_property(TARGET openpose_producer APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_producer PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_producer.so"
  IMPORTED_SONAME_RELEASE "libopenpose_producer.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_producer )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_producer "${_IMPORT_PREFIX}/lib/libopenpose_producer.so" )

# Import target "openpose_thread" for configuration "Release"
set_property(TARGET openpose_thread APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_thread PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_thread.so"
  IMPORTED_SONAME_RELEASE "libopenpose_thread.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_thread )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_thread "${_IMPORT_PREFIX}/lib/libopenpose_thread.so" )

# Import target "openpose_tracking" for configuration "Release"
set_property(TARGET openpose_tracking APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_tracking PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_tracking.so"
  IMPORTED_SONAME_RELEASE "libopenpose_tracking.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_tracking )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_tracking "${_IMPORT_PREFIX}/lib/libopenpose_tracking.so" )

# Import target "openpose_unity" for configuration "Release"
set_property(TARGET openpose_unity APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_unity PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_unity.so"
  IMPORTED_SONAME_RELEASE "libopenpose_unity.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_unity )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_unity "${_IMPORT_PREFIX}/lib/libopenpose_unity.so" )

# Import target "openpose_utilities" for configuration "Release"
set_property(TARGET openpose_utilities APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_utilities PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_utilities.so"
  IMPORTED_SONAME_RELEASE "libopenpose_utilities.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_utilities )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_utilities "${_IMPORT_PREFIX}/lib/libopenpose_utilities.so" )

# Import target "openpose_wrapper" for configuration "Release"
set_property(TARGET openpose_wrapper APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose_wrapper PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose_wrapper.so"
  IMPORTED_SONAME_RELEASE "libopenpose_wrapper.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose_wrapper )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose_wrapper "${_IMPORT_PREFIX}/lib/libopenpose_wrapper.so" )

# Import target "openpose" for configuration "Release"
set_property(TARGET openpose APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(openpose PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libopenpose.so.1.7.0"
  IMPORTED_SONAME_RELEASE "libopenpose.so.1.7.0"
  )

list(APPEND _IMPORT_CHECK_TARGETS openpose )
list(APPEND _IMPORT_CHECK_FILES_FOR_openpose "${_IMPORT_PREFIX}/lib/libopenpose.so.1.7.0" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
